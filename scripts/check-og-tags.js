#!/usr/bin/env node

const http = require('http');

const port = process.argv[2] || 3000;
const baseUrl = `http://localhost:${port}`;

console.log(`🔍 Checking OpenGraph meta tags on ${baseUrl}...\n`);

function fetchPageContent(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve(data);
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

function extractMetaTags(html) {
  const metaTags = {};
  
  // Extract OpenGraph tags
  const ogRegex = /<meta\s+property="og:([^"]+)"\s+content="([^"]+)"/g;
  let match;
  while ((match = ogRegex.exec(html)) !== null) {
    metaTags[`og:${match[1]}`] = match[2];
  }
  
  // Extract Twitter tags
  const twitterRegex = /<meta\s+name="twitter:([^"]+)"\s+content="([^"]+)"/g;
  while ((match = twitterRegex.exec(html)) !== null) {
    metaTags[`twitter:${match[1]}`] = match[2];
  }
  
  // Extract basic meta tags
  const basicRegex = /<meta\s+name="([^"]+)"\s+content="([^"]+)"/g;
  while ((match = basicRegex.exec(html)) !== null) {
    if (match[1] === 'description') {
      metaTags['description'] = match[2];
    }
  }
  
  // Extract title
  const titleMatch = html.match(/<title>([^<]+)<\/title>/);
  if (titleMatch) {
    metaTags['title'] = titleMatch[1];
  }
  
  return metaTags;
}

async function checkPage(path = '') {
  const url = `${baseUrl}${path}`;
  console.log(`📄 Checking: ${url}`);
  console.log('─'.repeat(50));
  
  try {
    const html = await fetchPageContent(url);
    const metaTags = extractMetaTags(html);
    
    // Check essential OG tags
    const essentialTags = [
      'title',
      'description', 
      'og:title',
      'og:description',
      'og:image',
      'og:url',
      'og:type',
      'twitter:card',
      'twitter:title',
      'twitter:description',
      'twitter:image'
    ];
    
    let foundTags = 0;
    
    for (const tag of essentialTags) {
      if (metaTags[tag]) {
        console.log(`✅ ${tag}: ${metaTags[tag]}`);
        foundTags++;
      } else {
        console.log(`❌ ${tag}: Missing`);
      }
    }
    
    console.log(`\n📊 Found ${foundTags}/${essentialTags.length} essential meta tags\n`);
    
    // Check if OG image is accessible
    if (metaTags['og:image']) {
      const imageUrl = metaTags['og:image'];
      console.log(`🖼️  Testing OG image: ${imageUrl}`);
      
      try {
        const imageCheck = await new Promise((resolve) => {
          const req = http.get(imageUrl, (res) => {
            resolve({
              status: res.statusCode,
              contentType: res.headers['content-type'],
              contentLength: res.headers['content-length']
            });
          });
          req.on('error', () => resolve({ status: 'ERROR' }));
          req.setTimeout(5000, () => {
            req.destroy();
            resolve({ status: 'TIMEOUT' });
          });
        });
        
        if (imageCheck.status === 200) {
          console.log(`✅ OG image accessible: ${imageCheck.contentType}, ${imageCheck.contentLength} bytes`);
        } else {
          console.log(`❌ OG image not accessible: ${imageCheck.status}`);
        }
      } catch (error) {
        console.log(`❌ Error checking OG image: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Error fetching page: ${error.message}`);
  }
}

async function main() {
  // Check homepage
  await checkPage('/');
  
  // Check other pages
  const pages = ['/en', '/de', '/o-nama', '/en/about', '/de/uber-uns'];
  
  for (const page of pages) {
    console.log('\n' + '='.repeat(60));
    await checkPage(page);
  }
  
  console.log('\n🎯 Testing Tips:');
  console.log('─'.repeat(50));
  console.log('• Test with Facebook Sharing Debugger: https://developers.facebook.com/tools/debug/');
  console.log('• Test with Twitter Card Validator: https://cards-dev.twitter.com/validator');
  console.log('• Test with LinkedIn Post Inspector: https://www.linkedin.com/post-inspector/');
}

main().catch(console.error);
