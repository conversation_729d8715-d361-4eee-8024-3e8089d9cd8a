#!/usr/bin/env node

const http = require('http');

const port = process.argv[2] || 3001;
const baseUrl = `http://localhost:${port}`;

console.log(`🔍 Checking if meta tags are in <head> vs <body> on ${baseUrl}...\n`);

function fetchPageContent(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve(data);
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

function analyzeMetaTagLocation(html) {
  // Split HTML into head and body sections
  const headMatch = html.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
  const bodyMatch = html.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
  
  if (!headMatch || !bodyMatch) {
    return { error: 'Could not parse HTML head/body sections' };
  }
  
  const headContent = headMatch[1];
  const bodyContent = bodyMatch[1];
  
  // Check for meta description
  const descInHead = /<meta\s+name="description"\s+content="[^"]+"/i.test(headContent);
  const descInBody = /<meta\s+name="description"\s+content="[^"]+"/i.test(bodyContent);
  
  // Check for OG tags
  const ogInHead = /<meta\s+property="og:[^"]+"\s+content="[^"]+"/i.test(headContent);
  const ogInBody = /<meta\s+property="og:[^"]+"\s+content="[^"]+"/i.test(bodyContent);
  
  // Check for Twitter tags
  const twitterInHead = /<meta\s+name="twitter:[^"]+"\s+content="[^"]+"/i.test(headContent);
  const twitterInBody = /<meta\s+name="twitter:[^"]+"\s+content="[^"]+"/i.test(bodyContent);
  
  // Extract actual meta description from head
  const descMatch = headContent.match(/<meta\s+name="description"\s+content="([^"]+)"/i);
  const description = descMatch ? descMatch[1] : null;
  
  return {
    description: {
      inHead: descInHead,
      inBody: descInBody,
      content: description
    },
    openGraph: {
      inHead: ogInHead,
      inBody: ogInBody
    },
    twitter: {
      inHead: twitterInHead,
      inBody: twitterInBody
    }
  };
}

async function checkPage(path = '') {
  const url = `${baseUrl}${path}`;
  console.log(`📄 Checking: ${url}`);
  console.log('─'.repeat(50));
  
  try {
    const html = await fetchPageContent(url);
    const analysis = analyzeMetaTagLocation(html);
    
    if (analysis.error) {
      console.log(`❌ ${analysis.error}`);
      return;
    }
    
    // Meta Description
    console.log('📝 Meta Description:');
    if (analysis.description.inHead && !analysis.description.inBody) {
      console.log(`✅ Correctly in <head>: "${analysis.description.content}"`);
    } else if (analysis.description.inBody) {
      console.log(`❌ Found in <body> (should be in <head>)`);
    } else if (!analysis.description.inHead) {
      console.log(`❌ Not found in <head>`);
    }
    
    // OpenGraph Tags
    console.log('\n🌐 OpenGraph Tags:');
    if (analysis.openGraph.inHead && !analysis.openGraph.inBody) {
      console.log(`✅ Correctly in <head>`);
    } else if (analysis.openGraph.inBody) {
      console.log(`❌ Found in <body> (should be in <head>)`);
    } else {
      console.log(`❌ Not found in <head>`);
    }
    
    // Twitter Tags
    console.log('\n🐦 Twitter Tags:');
    if (analysis.twitter.inHead && !analysis.twitter.inBody) {
      console.log(`✅ Correctly in <head>`);
    } else if (analysis.twitter.inBody) {
      console.log(`❌ Found in <body> (should be in <head>)`);
    } else {
      console.log(`❌ Not found in <head>`);
    }
    
  } catch (error) {
    console.log(`❌ Error fetching page: ${error.message}`);
  }
}

async function main() {
  await checkPage('/');
  
  console.log('\n' + '='.repeat(60));
  await checkPage('/en');
  
  console.log('\n' + '='.repeat(60));
  await checkPage('/o-nama');
}

main().catch(console.error);
