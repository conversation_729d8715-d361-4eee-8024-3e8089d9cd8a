# Deployment Guide

This guide explains how the base URL detection works across different environments.

## Base URL Detection Logic

The application automatically detects the correct base URL based on the environment:

### Priority Order:
1. **`NEXT_PUBLIC_BASE_URL`** - Explicit override (highest priority)
2. **`VERCEL_URL`** - Automatic Vercel detection
3. **Development** - `http://localhost:3000`
4. **Production Fallback** - `https://albatrosdoc.com`

## Environment Configurations

### 🏠 Local Development
```bash
# Automatically detected
NODE_ENV=development
# Results in: http://localhost:3000
```

### 🚀 Vercel Preview Deployments
```bash
# Automatically provided by Vercel
VERCEL_URL=albatrosdoc-git-main-username.vercel.app
# Results in: https://albatrosdoc-git-main-username.vercel.app
```

### 🌐 Vercel Production (albatrosdoc.vercel.app)
```bash
# Automatically provided by Vercel
VERCEL_URL=albatrosdoc.vercel.app
# Results in: https://albatrosdoc.vercel.app
```

### 🏢 Custom Domain Production
```bash
# Set this environment variable in Vercel dashboard
NEXT_PUBLIC_BASE_URL=https://albatrosdoc.com
# Results in: https://albatrosdoc.com
```

## Vercel Deployment Setup

### 1. Connect Repository
- Connect your GitHub repository to Vercel
- Vercel will automatically detect it's a Next.js project

### 2. Environment Variables (Recommended)
Set this in your Vercel project settings to ensure stable URLs for OG images:

```bash
# For stable Vercel URL (recommended for social sharing)
NEXT_PUBLIC_BASE_URL=https://albatrosdoc.vercel.app

# Or for custom domain
NEXT_PUBLIC_BASE_URL=https://albatrosdoc.com
```

**Why this is important**: Preview deployments have long URLs like `albatrosdoc-arqnbaxpl-saudbarudanovics-projects.vercel.app` that require verification steps, which breaks OG image loading for social media crawlers.

### 3. Domain Configuration
- **Preview**: `https://albatrosdoc.vercel.app` (automatic)
- **Custom Domain**: Add `albatrosdoc.com` in Vercel dashboard

## Testing Base URL Detection

Run the test script to verify base URL detection:

```bash
node scripts/test-baseurl.js
```

## SEO Implications

The base URL affects:
- **OpenGraph images**: `{baseUrl}/images/og-image.png`
- **Canonical URLs**: `{baseUrl}/page-path`
- **Language alternates**: `{baseUrl}/en`, `{baseUrl}/de`
- **Sitemap URLs**: `{baseUrl}/sitemap.xml`

## Troubleshooting

### Issue: OG images not loading on social media (Vercel preview URLs)
**Problem**: URLs like `albatrosdoc-arqnbaxpl-saudbarudanovics-projects.vercel.app` require verification
**Solution**: Set `NEXT_PUBLIC_BASE_URL=https://albatrosdoc.vercel.app` in Vercel environment variables

### Issue: Wrong base URL in meta tags
**Solution**: Check environment variables in Vercel dashboard

### Issue: OG images not loading
**Solution**: Verify the base URL includes the correct protocol (https://)

### Issue: Canonical URLs pointing to wrong domain
**Solution**: Set `NEXT_PUBLIC_BASE_URL` explicitly in production

## Verification Checklist

After deployment, verify:
- [ ] Favicon loads correctly
- [ ] OG image accessible at `{baseUrl}/images/og-image.png`
- [ ] Canonical URLs point to correct domain
- [ ] Sitemap accessible at `{baseUrl}/sitemap.xml`
- [ ] Language alternates work correctly

## Testing Tools

Use these scripts to verify your deployment:
```bash
# Check favicons and OG image
node scripts/check-favicons.js

# Check OpenGraph meta tags
node scripts/check-og-tags.js

# Test base URL detection
node scripts/test-baseurl.js
```
