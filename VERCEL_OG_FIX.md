# Fix OG Images on Vercel

## The Problem
Your OG images are using URLs like:
```
https://albatrosdoc-arqnbaxpl-saudbarudanovics-projects.vercel.app/images/og-image.png
```

These long preview URLs require verification steps that break social media crawlers.

## The Solution

### Option 1: Set Environment Variable (Recommended)
1. Go to your Vercel project dashboard
2. Navigate to **Settings** → **Environment Variables**
3. Add a new environment variable:
   - **Name**: `NEXT_PUBLIC_BASE_URL`
   - **Value**: `https://albatrosdoc.vercel.app`
   - **Environment**: All (Production, Preview, Development)
4. Redeploy your project

### Option 2: Code-based Fix (Already Implemented)
The code now automatically detects long preview URLs and uses the stable URL instead:

```typescript
// Preview URL: albatrosdoc-arqnbaxpl-saudbarudanovics-projects.vercel.app
// Becomes: albatrosdoc.vercel.app
```

## Verification

After implementing either solution, your OG images will use:
```
https://albatrosdoc.vercel.app/images/og-image.png
```

### Test Your Fix:
1. **Facebook Sharing Debugger**: https://developers.facebook.com/tools/debug/
2. **Twitter Card Validator**: https://cards-dev.twitter.com/validator
3. **LinkedIn Post Inspector**: https://www.linkedin.com/post-inspector/

### Quick Test:
```bash
# Check if OG image loads without verification
curl -I https://albatrosdoc.vercel.app/images/og-image.png
# Should return: HTTP/2 200
```

## Why This Happens
- Vercel preview deployments get long, unique URLs
- These URLs require verification for security
- Social media crawlers can't pass verification
- Result: OG images don't load when sharing

## Expected Results
✅ **Before Fix**: `albatrosdoc-arqnbaxpl-saudbarudanovics-projects.vercel.app/images/og-image.png` (broken)
✅ **After Fix**: `albatrosdoc.vercel.app/images/og-image.png` (working)

Your social media shares will now display the proper OG image!
