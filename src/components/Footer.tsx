'use client';

import { useTranslations, useLocale } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { getLocalizedRoute } from '@/config/routes';

const Footer = () => {
  const t = useTranslations('nav');
  const footer = useTranslations('footer');
  const locale = useLocale();

  return (
    <footer className="bg-black text-white pt-24 pb-6 relative overflow-hidden">

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-12">
          {/* Logo and Description */}
          <div className="md:col-span-2">
            <div className="flex items-center mb-8">
              <Link href={getLocalizedRoute('home', locale as 'bs' | 'en' | 'de')} className="w-16 h-16 relative hover:scale-105 transition-transform duration-300">
                <Image
                  src="/logo.svg"
                  alt="AlbatrosDoc Logo"
                  width={64}
                  height={64}
                  className="w-full h-full object-contain"
                  style={{
                    filter: 'brightness(0) saturate(100%) invert(98%) sepia(8%) saturate(346%) hue-rotate(75deg) brightness(106%) contrast(96%)'
                  }}
                />
              </Link>
            </div>
            <p className="text-gray-300 leading-relaxed max-w-lg text-lg mb-8">
              {footer('description')}
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-xl mb-6 text-white">{footer('quickLinks')}</h3>
            <ul className="space-y-4">
              <li>
                <Link href={getLocalizedRoute('home', locale as 'bs' | 'en' | 'de')} className="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-2 inline-block group">
                  <span className="flex items-center">
                    {t('home')}
                    <svg className="w-4 h-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </span>
                </Link>
              </li>
              <li>
                <Link href={getLocalizedRoute('about', locale as 'bs' | 'en' | 'de')} className="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-2 inline-block group">
                  <span className="flex items-center">
                    {t('about')}
                    <svg className="w-4 h-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </span>
                </Link>
              </li>
              <li>
                <Link href={getLocalizedRoute('services', locale as 'bs' | 'en' | 'de')} className="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-2 inline-block group">
                  <span className="flex items-center">
                    {t('services')}
                    <svg className="w-4 h-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </span>
                </Link>
              </li>
              <li>
                <Link href={getLocalizedRoute('contact', locale as 'bs' | 'en' | 'de')} className="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-2 inline-block group">
                  <span className="flex items-center">
                    {t('contact')}
                    <svg className="w-4 h-4 ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-semibold text-xl mb-6 text-white">{footer('contactTitle')}</h3>
            <div className="space-y-4 text-gray-300">
              <div>
                <p className="hover:text-white transition-colors"><EMAIL></p>
              </div>
              <div>
                <p className="hover:text-white transition-colors">+387 61 859 534</p>
              </div>
              <div>
                <p className="hover:text-white transition-colors">Sarajevo, BiH</p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-16 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <p className="text-gray-400 text-sm">
              © {new Date().getFullYear()} {footer('copyright')}
            </p>
            <div className="flex space-x-4 mt-6 md:mt-0">
            <a href="https://navhaus.com" target="_blank" rel="noopener noreferrer" className="flex items-center text-albatros-ivory hover:[filter:invert(20%)_sepia(59%)_saturate(3817%)_hue-rotate(345deg)_brightness(105%)_contrast(115%)] transition-all">
              <span className="mr-[5px] pt-2.5">{footer('builtBy')}</span>
              <Image
                src="/images/navhaus-logo.svg"
                alt="Navhaus Logo"
                width={60}
                height={10}
                className="h-5 w-auto"
              />
            </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
