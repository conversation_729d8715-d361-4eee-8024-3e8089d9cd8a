import Navigation from '@/components/Navigation';
import AboutHeroSection from '@/components/about/AboutHeroSection';
import AboutStorySection from '@/components/about/AboutStorySection';
import AboutValuesSection from '@/components/about/AboutValuesSection';
import AboutTeamSection from '@/components/about/AboutTeamSection';
import CTASection from '@/components/CTASection';
import Footer from '@/components/Footer';
import { generatePageMetadata } from '@/lib/metadata';
import type { Metadata } from 'next';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const pathname = locale === 'bs' ? '/o-nama' : locale === 'en' ? '/en/about' : '/de/uber-uns';
  return generatePageMetadata('about', locale, pathname);
}

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      <Navigation />
      <main>
        <AboutHeroSection />
        <AboutStorySection />
        <AboutValuesSection />
        <AboutTeamSection />
        <CTASection />
      </main>
      <Footer />
    </div>
  );
}
