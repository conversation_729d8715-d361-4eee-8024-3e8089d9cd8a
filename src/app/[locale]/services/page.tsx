import Navigation from '@/components/Navigation';
import ServicesHeroSection from '@/components/services/ServicesHeroSection';
import ServicesListSection from '@/components/services/ServicesListSection';
import PowerOfAttorneySection from '@/components/services/PowerOfAttorneySection';
import CTASection from '@/components/CTASection';
import Footer from '@/components/Footer';
import { generatePageMetadata } from '@/lib/metadata';
import type { Metadata } from 'next';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const pathname = locale === 'bs' ? '/usluge' : locale === 'en' ? '/en/services' : '/de/dienstleistungen';
  return generatePageMetadata('services', locale, pathname);
}

export default function ServicesPage() {
  return (
    <div className="min-h-screen">
      <Navigation />
      <main>
        <ServicesHeroSection />
        <ServicesListSection />
        <PowerOfAttorneySection />
        <CTASection />
      </main>
      <Footer />
    </div>
  );
}
