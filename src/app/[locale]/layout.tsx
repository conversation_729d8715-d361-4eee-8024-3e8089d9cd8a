import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import HtmlLangSetter from '@/components/HtmlLangSetter';

// Note: Metadata is now handled by individual pages

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  await params; // Ensure params are awaited for Next.js
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <HtmlLangSetter />
      {children}
    </NextIntlClientProvider>
  );
}
