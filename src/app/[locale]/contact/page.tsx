import Navigation from '@/components/Navigation';
import ContactHeroSection from '@/components/contact/ContactHeroSection';
import ContactFormSection from '@/components/contact/ContactFormSection';
import ContactInfoSection from '@/components/contact/ContactInfoSection';
import Footer from '@/components/Footer';
import { generatePageMetadata } from '@/lib/metadata';
import type { Metadata } from 'next';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const pathname = locale === 'bs' ? '/kontakt' : locale === 'en' ? '/en/contact' : '/de/kontakt';
  return generatePageMetadata('contact', locale, pathname);
}

export default function ContactPage() {
  return (
    <div className="min-h-screen">
      <Navigation />
      <main>
        <ContactHeroSection />
        <ContactFormSection />
        <ContactInfoSection />
      </main>
      <Footer />
    </div>
  );
}
