import { NextResponse } from 'next/server';

export async function GET() {
  const baseUrl = 'https://albatrosdoc.com'; // Update this to your actual domain
  const currentDate = new Date().toISOString();

  // Define page groups with their localized versions and hreflang attributes
  const pageGroups = [
    {
      name: 'homepage',
      priority: '1.0',
      changefreq: 'weekly',
      pages: [
        { url: '/', hreflang: 'bs' },
        { url: '/en', hreflang: 'en' },
        { url: '/de', hreflang: 'de' },
      ]
    },
    {
      name: 'about',
      priority: '0.8',
      changefreq: 'monthly',
      pages: [
        { url: '/o-nama', hreflang: 'bs' },
        { url: '/en/about', hreflang: 'en' },
        { url: '/de/uber-uns', hreflang: 'de' },
      ]
    },
    {
      name: 'services',
      priority: '0.9',
      changefreq: 'weekly',
      pages: [
        { url: '/usluge', hreflang: 'bs' },
        { url: '/en/services', hreflang: 'en' },
        { url: '/de/dienstleistungen', hreflang: 'de' },
      ]
    },
    {
      name: 'contact',
      priority: '0.7',
      changefreq: 'monthly',
      pages: [
        { url: '/kontakt', hreflang: 'bs' },
        { url: '/en/contact', hreflang: 'en' },
        { url: '/de/kontakt', hreflang: 'de' },
      ]
    },
  ];

  // Generate sitemap with hreflang attributes
  const generateUrlEntry = (group: typeof pageGroups[0]) => {
    return group.pages.map(page => {
      const alternateLinks = group.pages
        .map(altPage => `    <xhtml:link rel="alternate" hreflang="${altPage.hreflang}" href="${baseUrl}${altPage.url}" />`)
        .join('\n');

      return `  <url>
    <loc>${baseUrl}${page.url}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>${group.changefreq}</changefreq>
    <priority>${group.priority}</priority>
${alternateLinks}
  </url>`;
    }).join('\n');
  };

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${pageGroups.map(generateUrlEntry).join('\n')}
</urlset>`;

  return new NextResponse(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
    },
  });
}
